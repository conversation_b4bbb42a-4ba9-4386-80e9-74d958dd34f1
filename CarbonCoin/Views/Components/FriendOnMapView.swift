//
//  FriendOnMapView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/29.
//

import SwiftUI

// MARK: - 地图上的好友显示组件

/// 在地图上显示好友位置信息的组件
struct FriendOnMapView: View {

    // MARK: - Properties

    /// 好友位置信息
    let friendLocation: FriendLocationInfo

    /// 头像大小
    let avatarSize: CGFloat

    /// 是否显示详细信息
    let showDetails: Bool

    /// 卡片拖放处理回调
    let onCardDropped: ((String) -> Void)?

    /// 道具拖放处理回调
    let onPropDropped: ((Int) -> Void)?

    // MARK: - Initialization

    init(
        friendLocation: FriendLocationInfo,
        avatarSize: CGFloat = 50,
        showDetails: Bool = true,
        onCardDropped: ((String) -> Void)? = nil,
        onPropDropped: ((Int) -> Void)? = nil
    ) {
        self.friendLocation = friendLocation
        self.avatarSize = avatarSize
        self.showDetails = showDetails
        self.onCardDropped = onCardDropped
        self.onPropDropped = onPropDropped
    }

    // MARK: - Body

    var body: some View {
        VStack(spacing: 4) {
            // 好友姓名（显示在头像上方）
            if showDetails {
                Text(friendLocation.nickname)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.textPrimary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.black.opacity(0.7))
                    )
                    .shadow(radius: 2)
            }

            // 好友头像
            ZStack {
                // 头像背景圆圈
                Circle()
                    .fill(Color.brandGreen)
                    .frame(width: avatarSize, height: avatarSize)
                    .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)

                // 在线状态指示器
                if friendLocation.isOnline {
                    Circle()
                        .stroke(Color.success, lineWidth: 3)
                        .frame(width: avatarSize + 6, height: avatarSize + 6)
                }

                // 头像图片
                AsyncImage(url: URL(string: friendLocation.avatarURL ?? "")) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: avatarSize - 4, height: avatarSize - 4)
                        .clipShape(Circle())
                } placeholder: {
                    // 默认头像占位符
                    Circle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: avatarSize - 4, height: avatarSize - 4)
                        .overlay(
                            Image(systemName: "person.fill")
                                .foregroundColor(.textSecondary)
                                .font(.system(size: avatarSize * 0.4))
                        )
                }
            }

            // 活跃时间信息（显示在头像下方）
            if showDetails {
                VStack(spacing: 2) {
                    // 在线状态文本
                    Text(friendLocation.isOnline ? "在线" : "离线")
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(friendLocation.isOnline ? .success : .textSecondary)

                    // 最后活跃时间
                    Text(friendLocation.formattedLastActiveTime)
                        .font(.caption2)
                        .foregroundColor(.textSecondary)
                }
                .padding(.horizontal, 6)
                .padding(.vertical, 3)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.black.opacity(0.7))
                )
                .shadow(radius: 1)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: friendLocation.isOnline)
        .onDrop(of: [.text], isTargeted: nil) { providers in
            // 处理卡片和道具拖放
            for provider in providers {
                if provider.canLoadObject(ofClass: NSString.self) {
                    provider.loadObject(ofClass: NSString.self) { itemId, error in
                        if let itemId = itemId as? String {
                            DispatchQueue.main.async {
                                // 判断是卡片ID还是道具ID
                                if let propId = Int(itemId) {
                                    // 是道具ID（数字）
                                    onPropDropped?(propId)
                                    print("🎯 道具 \(propId) 拖放到好友 \(friendLocation.nickname)")
                                } else {
                                    // 是卡片ID（字符串）
                                    onCardDropped?(itemId)
                                    print("🎯 卡片 \(itemId) 拖放到好友 \(friendLocation.nickname)")
                                }
                            }
                        }
                    }
                }
            }
            return true
        }
    }
}

// MARK: - 预览

#Preview("单个好友 - 在线") {
    let onlineFriend = FriendLocationInfo(
        userId: "alice",
        nickname: "Alice",
        avatarURL: nil,
        lastActiveTime: Date(),
        latitude: 39.9042,
        longitude: 116.4074,
        lastUpdate: Date(),
        lastOnlineTime: Date()
    )

    return FriendOnMapView(friendLocation: onlineFriend)
        .padding()
        .background(Color.black)
        .previewLayout(.sizeThatFits)
}

#Preview("单个好友 - 离线") {
    let offlineFriend = FriendLocationInfo(
        userId: "bob",
        nickname: "Bob",
        avatarURL: nil,
        lastActiveTime: Date().addingTimeInterval(-3600), // 1小时前
        latitude: 39.9042,
        longitude: 116.4074,
        lastUpdate: Date().addingTimeInterval(-3600),
        lastOnlineTime: Date().addingTimeInterval(-600) // 10分钟前
    )

    return FriendOnMapView(friendLocation: offlineFriend)
        .padding()
        .background(Color.black)
        .previewLayout(.sizeThatFits)
}

#Preview("小尺寸头像") {
    let friend = FriendLocationInfo(
        userId: "charlie",
        nickname: "Charlie",
        avatarURL: nil,
        lastActiveTime: Date(),
        latitude: 39.9042,
        longitude: 116.4074,
        lastUpdate: Date(),
        lastOnlineTime: Date()
    )

    return FriendOnMapView(
        friendLocation: friend,
        avatarSize: 30,
        showDetails: false
    )
    .padding()
    .background(Color.black)
    .previewLayout(.sizeThatFits)
}

#Preview("多个好友对比") {
    let friends = [
        FriendLocationInfo(
            userId: "alice",
            nickname: "Alice",
            avatarURL: nil,
            lastActiveTime: Date(),
            latitude: 39.9042,
            longitude: 116.4074,
            lastUpdate: Date(),
            lastOnlineTime: Date()
        ),
        FriendLocationInfo(
            userId: "bob",
            nickname: "Bob",
            avatarURL: nil,
            lastActiveTime: Date().addingTimeInterval(-1800), // 30分钟前
            latitude: 39.9042,
            longitude: 116.4074,
            lastUpdate: Date().addingTimeInterval(-1800),
            lastOnlineTime: Date().addingTimeInterval(-600) // 10分钟前
        ),
        FriendLocationInfo(
            userId: "charlie",
            nickname: "Charlie",
            avatarURL: nil,
            lastActiveTime: Date().addingTimeInterval(-7200), // 2小时前
            latitude: 39.9042,
            longitude: 116.4074,
            lastUpdate: Date().addingTimeInterval(-7200),
            lastOnlineTime: Date().addingTimeInterval(-1800) // 30分钟前
        )
    ]

    return HStack(spacing: 20) {
        ForEach(friends, id: \.userId) { friend in
            FriendOnMapView(friendLocation: friend, avatarSize: 40)
        }
    }
    .padding()
    .background(Color.black)
    .previewLayout(.sizeThatFits)
}
