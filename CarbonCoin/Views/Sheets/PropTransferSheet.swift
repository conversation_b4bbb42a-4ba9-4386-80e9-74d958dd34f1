//
//  PropTransferSheet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import SwiftUI

// MARK: - 道具Sheet视图

/// 道具Sheet视图，参考 ItemCardSheetView 实现拖拽功能
struct PropTransferSheet: View {
    // MARK: - Properties
    @State private var sheetHeight: CGFloat = UIScreen.main.bounds.height / 4 // 初始高度
    @State private var isDragging = false
    @State private var draggedProp: PropInfo?

    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    private let fullHeight: CGFloat
    private let compactHeight: CGFloat

    // 拖拽处理回调
    let onPropDropped: (Int, String) -> Void // (propId, userId)
    let onGlobalDragStarted: ((PropInfo, CGPoint) -> Void)?
    let onGlobalDragChanged: ((CGPoint) -> Void)?
    let onGlobalDragEnded: (() -> Void)?

    // MARK: - Initialization

    init(
        onPropDropped: @escaping (Int, String) -> Void,
        onGlobalDragStarted: ((PropInfo, CGPoint) -> Void)? = nil,
        onGlobalDragChanged: ((CGPoint) -> Void)? = nil,
        onGlobalDragEnded: (() -> Void)? = nil
    ) {
        self.onPropDropped = onPropDropped
        self.onGlobalDragStarted = onGlobalDragStarted
        self.onGlobalDragChanged = onGlobalDragChanged
        self.onGlobalDragEnded = onGlobalDragEnded

        // 计算高度
        self.fullHeight = screenHeight * 0.7
        self.compactHeight = screenHeight / 4
    }

    // MARK: - Body

    var body: some View {
        NavigationView {
            VStack(spacing: Theme.Spacing.lg) {
                // 顶部标题区域
                headerView

                // 道具选择区域
                propSelectionView

                // 备注输入区域
                remarkInputView

                // 好友选择区域
                friendSelectionView

                Spacer()
            }
            .padding(Theme.Spacing.lg)
//            .background(Color.backgroundPrimar.ignoresSafeArea())
            .navigationTitle("发送道具")
            .navigationBarTitleDisplayMode(.inline)
        }
        .onAppear {
            // 加载好友位置信息
            Task {
                await friendMapViewModel.loadFriendLocations(for: appSettings.userId)
            }
        }
        .alert("发送成功", isPresented: .constant(propInteractionViewModel.errorMessage == nil && !propInteractionViewModel.isLoading)) {
            Button("确定") {
                dismiss()
            }
        } message: {
            Text("道具已成功发送给好友！")
        }
//        .alert("发送失败", isPresented: .constant(propInteractionViewModel.errorMessage != nil)) {
//            Button("确定") {
//                propInteractionViewModel.clearMessages()
//            }
//        } message: {
//            if let errorMessage = propInteractionViewModel.errorMessage {
//                Text(errorMessage)
//            }
//        }
    }

    // MARK: - Subviews

    /// 顶部标题区域
    private var headerView: some View {
        VStack(spacing: Theme.Spacing.sm) {
            Image(systemName: "gift.fill")
                .font(.system(size: 40))
                .foregroundColor(.brandGreen)

            Text("选择要发送的道具")
                .font(.title2Brand)
                .foregroundColor(.textPrimary)
                .fontWeight(.semibold)

            Text("选择道具后拖拽到好友头像即可发送")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, Theme.Spacing.md)
    }

    /// 道具选择区域
    private var propSelectionView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("可用道具")
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(.medium)

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: Theme.Spacing.md) {
                    ForEach(PropInteractionManager.shared.getAllPropInfos()) { propInfo in
                        PropItemView(
                            propInfo: propInfo,
                            isSelected: selectedPropId == propInfo.propId,
                            onTap: {
                                selectedPropId = propInfo.propId
                                customRemark = propInfo.defaultRemark
                            }
                        )
                    }
                }
                .padding(.horizontal, Theme.Spacing.sm)
            }
        }
        .padding(.vertical, Theme.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.cardBackground.opacity(0.3))
        )
    }

    /// 备注输入区域
    private var remarkInputView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            Text("附加消息")
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(.medium)

            TextField("输入想对好友说的话...", text: $customRemark, axis: .vertical)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .padding(Theme.Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.cardBackground.opacity(0.5))
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.brandGreen.opacity(0.3), lineWidth: 1)
                        )
                )
                .lineLimit(3...6)
        }
    }

    /// 好友选择区域
    private var friendSelectionView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("选择好友")
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(.medium)

            if friendMapViewModel.friendAnnotations.isEmpty {
                // 空状态
                VStack(spacing: Theme.Spacing.sm) {
                    Image(systemName: "person.2.slash")
                        .font(.system(size: 30))
                        .foregroundColor(.textSecondary)

                    Text("暂无在线好友")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)

                    Button("刷新") {
                        Task {
                            await friendMapViewModel.refreshFriendLocations(for: appSettings.userId)
                        }
                    }
                    .font(.captionBrand)
                    .foregroundColor(.brandGreen)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, Theme.Spacing.xl)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.cardBackground.opacity(0.3))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.textSecondary.opacity(0.2), lineWidth: 1)
                        )
                )
            } else {
                // 好友列表
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: Theme.Spacing.md), count: 3), spacing: Theme.Spacing.md) {
                    ForEach(friendMapViewModel.friendAnnotations) { annotation in
                        FriendAvatarView(
                            friendLocation: annotation.friendLocation,
                            isSelected: false,
                            onTap: {
                                sendPropToFriend(userId: annotation.friendLocation.userId)
                            }
                        )
                    }
                }
                .padding(Theme.Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(Color.cardBackground.opacity(0.3))
                )
            }
        }
    }

    // MARK: - Helper Methods

    /// 发送道具给好友
    private func sendPropToFriend(userId: String) {
        guard let propId = selectedPropId else {
            // 显示提示：请先选择道具
            return
        }

        let remark = customRemark.isEmpty ? nil : customRemark

        Task {
            await propInteractionViewModel.createPropInteraction(
                receiverUserId: userId,
                propId: propId,
                remark: remark
            )
        }
    }
}

// MARK: - 道具项视图

/// 单个道具项的展示视图
private struct PropItemView: View {
    let propInfo: PropInfo
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        VStack(spacing: Theme.Spacing.sm) {
            // 道具动画
            LottieHelperView(
                fileName: "\(propInfo.propTitle).json",
                contentMode: .scaleAspectFit,
                playLoopMode: .loop,
                animationProgress: 1
            )
            .frame(width: 60, height: 60)
            .background(
                Circle()
                    .fill(Color.brandGreen.opacity(0.1))
                    .overlay(
                        Circle()
                            .stroke(isSelected ? Color.brandGreen : Color.clear, lineWidth: 2)
                    )
            )

            // 道具名称
            Text(propInfo.propTitle)
                .font(.captionBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(isSelected ? .semibold : .regular)
                .lineLimit(1)
        }
        .frame(width: 80)
        .padding(.vertical, Theme.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? Color.brandGreen.opacity(0.1) : Color.clear)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isSelected ? Color.brandGreen : Color.clear, lineWidth: 1)
                )
        )
        .scaleEffect(isSelected ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - 好友头像视图

/// 好友头像选择视图
private struct FriendAvatarView: View {
    let friendLocation: FriendLocation
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        VStack(spacing: Theme.Spacing.xs) {
            // 头像
            AsyncImage(url: URL(string: friendLocation.avatarURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Image(systemName: "person.circle.fill")
                    .font(.title)
                    .foregroundColor(.textSecondary)
            }
            .frame(width: 50, height: 50)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(isSelected ? Color.brandGreen : Color.textSecondary.opacity(0.3), lineWidth: 2)
            )

            // 昵称
            Text(friendLocation.nickname)
                .font(.caption2)
                .foregroundColor(.textPrimary)
                .lineLimit(1)
                .frame(maxWidth: 60)
        }
        .padding(.vertical, Theme.Spacing.xs)
        .scaleEffect(isSelected ? 1.1 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .onTapGesture {
            onTap()
        }
    }
}

// MARK: - Preview

#Preview {
    PropTransferSheet()
        .environmentObject(AppSettings())
}
