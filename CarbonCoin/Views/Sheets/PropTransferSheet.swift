//
//  PropTransferSheet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import SwiftUI

// MARK: - 道具Sheet视图

/// 道具Sheet视图，参考 ItemCardSheetView 实现拖拽功能
struct PropTransferSheet: View {
    // MARK: - Properties
    @State private var sheetHeight: CGFloat = UIScreen.main.bounds.height / 4 // 初始高度
    @State private var isDragging = false
    @State private var draggedProp: PropInfo?

    // 屏幕尺寸
    private let screenHeight = UIScreen.main.bounds.height
    private let fullHeight: CGFloat
    private let compactHeight: CGFloat

    // 拖拽处理回调
    let onGlobalDragStarted: ((PropInfo, CGPoint) -> Void)?
    let onGlobalDragChanged: ((CGPoint) -> Void)?
    let onGlobalDragEnded: (() -> Void)?

    // MARK: - Initialization
    
    init(
        onGlobalDragStarted: ((PropInfo, CGPoint) -> Void)? = nil,
        onGlobalDragChanged: ((CGPoint) -> Void)? = nil,
        onGlobalDragEnded: (() -> Void)? = nil
    ) {
        self.onGlobalDragStarted = onGlobalDragStarted
        self.onGlobalDragChanged = onGlobalDragChanged
        self.onGlobalDragEnded = onGlobalDragEnded
        
        // 计算高度
        self.fullHeight = screenHeight * 0.7
        self.compactHeight = screenHeight / 4
    }
    
    // MARK: - Body
    
    var body: some View {
        VStack(spacing: 0) {
            // 拖拽指示器
            dragIndicator
            
            // 标题栏
            headerView
            
            // 道具滚动视图
            propScrollView
        }
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .ignoresSafeArea(edges: .bottom)
        )
        .frame(height: sheetHeight)
        .gesture(
            DragGesture()
                .onChanged { value in
                    let newHeight = sheetHeight - value.translation.height
                    sheetHeight = max(compactHeight, min(fullHeight, newHeight))
                }
                .onEnded { value in
                    withAnimation(.spring()) {
                        if value.translation.height > 100 {
                            sheetHeight = compactHeight
                        } else if value.translation.height < -100 {
                            sheetHeight = fullHeight
                        } else {
                            sheetHeight = sheetHeight > (compactHeight + fullHeight) / 2 ? fullHeight : compactHeight
                        }
                    }
                }
        )
    }
    
    // MARK: - Subviews
    
    /// 拖拽指示器
    private var dragIndicator: some View {
        RoundedRectangle(cornerRadius: 2.5)
            .fill(Color.textSecondary.opacity(0.5))
            .frame(width: 40, height: 5)
            .padding(.top, Theme.Spacing.sm)
    }

    // MARK: - 标题栏
    private var headerView: some View {
        HStack {
            Text("我的道具")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Spacer()

            Text("\(PropInteractionManager.shared.getAllPropInfos().count) 个")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.vertical, Theme.Spacing.sm)
    }

    // MARK: - 道具滚动视图
    private var propScrollView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Theme.Spacing.md) {
                ForEach(PropInteractionManager.shared.getAllPropInfos()) { propInfo in
                    DraggablePropThumbnail(
                        propInfo: propInfo,
                        onDragStarted: handleDragStarted,
                        onDragChanged: handleDragChanged,
                        onDragEnded: handleDragEnded
                    )
                }
            }
            .padding(.horizontal, Theme.Spacing.md)
        }
        .frame(height: 160)
    }

    // MARK: - 拖拽处理方法

    /// 处理拖拽开始
    private func handleDragStarted(_ propInfo: PropInfo) {
        draggedProp = propInfo
        isDragging = true
        print("🎯 开始拖拽道具: \(propInfo.propTitle)")

        // 触发全局拖拽开始
        if let onGlobalDragStarted = onGlobalDragStarted {
            // 计算道具在屏幕中的初始位置
            let screenCenter = CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height * 0.8)
            onGlobalDragStarted(propInfo, screenCenter)
        }
    }

    /// 处理拖拽状态变化
    private func handleDragChanged(_ propInfo: PropInfo, _ value: DragGesture.Value) {
        draggedProp = propInfo
        isDragging = true

        // 更新全局拖拽位置
        if let onGlobalDragChanged = onGlobalDragChanged {
            let globalPosition = CGPoint(
                x: value.location.x,
                y: value.location.y
            )
            onGlobalDragChanged(globalPosition)
        }
    }

    /// 处理拖拽结束
    private func handleDragEnded(_ propInfo: PropInfo, _ value: DragGesture.Value) {
        isDragging = false
        draggedProp = nil
        print("🎯 结束拖拽道具: \(propInfo.propTitle)")

        // 触发全局拖拽结束
        onGlobalDragEnded?()
    }
}

// MARK: - 可拖拽道具缩略图

/// 可拖拽的道具缩略图组件
private struct DraggablePropThumbnail: View {
    let propInfo: PropInfo
    let onDragStarted: (PropInfo) -> Void
    let onDragChanged: (PropInfo, DragGesture.Value) -> Void
    let onDragEnded: (PropInfo, DragGesture.Value) -> Void

    @State private var isDragging = false
    @GestureState private var gestureState = DragState.inactive

    // 拖拽状态枚举
    enum DragState {
        case inactive
        case longPressing
        case dragging(translation: CGSize)

        var translation: CGSize {
            switch self {
            case .dragging(let translation):
                return translation
            default:
                return .zero
            }
        }

        var isActive: Bool {
            switch self {
            case .inactive:
                return false
            default:
                return true
            }
        }
    }

    var body: some View {
        propContent
            .onTapGesture {
                // 只有在非拖拽状态下才允许点击
                if !gestureState.isActive && !isDragging {
                    print("点击了道具: \(propInfo.propTitle)")
                }
            }
    }

    // MARK: - 道具内容视图
    private var propContent: some View {
        VStack(alignment: .center, spacing: Theme.Spacing.xs) {
            // 道具动画
            LottieHelperView(
                fileName: "\(propInfo.propTitle).json",
                contentMode: .scaleAspectFit,
                playLoopMode: .loop,
                animationProgress: 1
            )
            .frame(width: 80, height: 80)
            .background(
                Circle()
                    .fill(Color.brandGreen.opacity(0.1))
                    .overlay(
                        Circle()
                            .stroke(Color.brandGreen.opacity(0.3), lineWidth: 1)
                    )
            )

            // 道具标题
            Text(propInfo.propTitle)
                .font(.captionBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(.medium)
                .lineLimit(1)
        }
        .frame(width: 120)
        .padding(Theme.Spacing.sm)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.cardBackground.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                        .stroke(Color.brandGreen.opacity(isDragging ? 0.8 : 0.3), lineWidth: 1)
                )
        )
        .scaleEffect(gestureState.isActive ? 1.05 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: gestureState.isActive)
        .onDrag {
            // 系统级拖拽支持 - 提供道具ID作为拖拽数据
            return NSItemProvider(object: "\(propInfo.propId)" as NSString)
        }
        .simultaneousGesture(
            // 使用 simultaneousGesture 避免与点击手势冲突
            LongPressGesture(minimumDuration: 0.5)
                .onEnded { _ in
                    // 长按开始拖拽模式
                    isDragging = true
                    onDragStarted(propInfo)
                }
        )
        .simultaneousGesture(
            DragGesture(coordinateSpace: .global)
                .onChanged { value in
                    if isDragging {
                        onDragChanged(propInfo, value)
                    }
                }
                .onEnded { value in
                    if isDragging {
                        onDragEnded(propInfo, value)
                        isDragging = false
                    }
                }
        )
    }
}

// MARK: - Preview

#Preview {
    PropTransferSheet()
}
